package com.ximalaya.galaxy.business.business.boss.impl

import com.ximalaya.galaxy.business.business.BusinessComponent
import com.ximalaya.galaxy.business.business.boss.BizStreamMessage
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.support.getAgentPendingMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.tuple.MutablePair
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock

/**
 *<AUTHOR>
 *@create 2025-06-05 21:32
 */
@BusinessComponent
class BizStreamMessageImpl(
    private val blockService: GalaxyBlockService,
    private val stringRedisTemplate: StringRedisTemplate,
) : BizStreamMessage {

    private var sseMap = ConcurrentHashMap<Pair<Long, Long>, MutablePair<SseEmitter, Boolean>>()

    override fun reconnectStreamChannel(
        emitter: SseEmitter,
        sessionCode: Long,
        phaseCode: Long
    ): Boolean {
        return doConnectStreamChannel(emitter, sessionCode, phaseCode, false)
    }

    override fun connectRealtimeStreamChannel(emitter: SseEmitter, sessionCode: Long, phaseCode: Long): Boolean {
        return doConnectStreamChannel(emitter, sessionCode, phaseCode, true)
    }

    private fun doConnectStreamChannel(
        emitter: SseEmitter,
        sessionCode: Long,
        phaseCode: Long,
        historyCompleted: Boolean
    ): Boolean {
        val connectRedisKey = RedisLockHelper.getPhaseConnectLockKey(sessionCode, phaseCode)
        // 设置有效期为 20 分钟
        val isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(connectRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn("不允许同时打开多个窗口, Session: {}, Phase: {}", sessionCode, phaseCode)

            emitter.send(ResultVo.failed<Unit>(ErrorCode.SESSION_ERROR, "不允许同时打开多个窗口"))
            emitter.complete()
            return false
        }

        // 初始状态：emitter已连接，但历史消息尚未完成发送
        sseMap[Pair(sessionCode, phaseCode)] = MutablePair(emitter, historyCompleted)
        return true
    }

    override fun releaseStreamChannel(sessionCode: Long, phaseCode: Long) {
        sseMap.remove(Pair(sessionCode, phaseCode))
        stringRedisTemplate.delete(RedisLockHelper.getPhaseConnectLockKey(sessionCode, phaseCode))
        // 清理待处理消息队列
        stringRedisTemplate.delete(getAgentPendingMessageCacheKey(sessionCode, phaseCode))
    }

    override fun sendSseWriteBackMessage(agentMessage: AgentContentProtocol) {
        val sessionCode = agentMessage._sessionCode ?: return
        val phaseCode = agentMessage._phaseCode ?: return
        val sessionPhasePair = agentMessage.toPair()

        val emitterPair = sseMap[sessionPhasePair] ?: return
        val (_, historyCompleted) = emitterPair

        // 如果历史消息已处理完成
        if (historyCompleted) {
            // 将当前消息添加到队列
            val pendingKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
            stringRedisTemplate.opsForList().rightPush(pendingKey, agentMessage.toJson())

            // 处理队列中的消息
            processAgentPendingMessageMessages(sessionCode, phaseCode)
            return
        }

        // 如果历史消息尚未处理完成
        // 尝试获取Redis锁来处理历史消息
        val historyLock = ReentrantLock()
        try {
            if (!historyLock.tryLock()) {
                // 如果无法获取锁，说明其他线程正在处理历史消息
                // 将当前消息添加到队列中
                val pendingKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
                stringRedisTemplate.opsForList().rightPush(pendingKey, agentMessage.toJson())
                stringRedisTemplate.expire(pendingKey, 20, TimeUnit.MINUTES)
                return
            }

            // double check 如果已经被其他线程处理完成，直接返回
            if (sseMap[sessionPhasePair]?.right == true) {
                // 将当前消息添加到队列
                val pendingKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
                stringRedisTemplate.opsForList().rightPush(pendingKey, agentMessage.toJson())
                // 处理队列中的消息
                processAgentPendingMessageMessages(sessionCode, phaseCode)
                return
            }

            // 处理历史消息
            val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
            val listSize = stringRedisTemplate.opsForList().size(messageKey) ?: 0

            if (listSize > 0) {
                // 获取Redis list中的所有消息并发送
                val messages = stringRedisTemplate.opsForList().range(messageKey, 0, listSize - 1)
                if (CollectionUtils.isNotEmpty(messages)) {
                    for (messageJson in messages!!) {
                        val message = AgentContentProtocol.parseJson(messageJson)
                        if (sendStream(sessionCode, phaseCode, message)) {
                            return
                        }
                    }
                }
            } else {
                // 从db查询历史消息 证明已经结束了
                val blocks = blockService.ktQuery()
                    .eq(GalaxyBlockEntity::sessionCode, sessionCode)
                    .eq(GalaxyBlockEntity::phaseCode, phaseCode)
                    .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
                    .list()

                if (CollectionUtils.isEmpty(blocks)) {
                    // 没有找到数据块 报错
                    logger.error("未找到会话数据块, Session: {}, Phase: {}", sessionCode, phaseCode)
                    sendAndStopStream(
                        sessionCode, phaseCode, ResultVo.failed<Unit>(
                            GalaxyException(
                                ErrorCode.SESSION_ERROR,
                                "数据不存在！！！"
                            )
                        )
                    )
                    return
                }
                // 发送数据块 后
                emitPhaseMessageFormDB(sessionCode, phaseCode, blocks)
                sendAndStopStream<Unit>(sessionCode, phaseCode)
                return
            }

            // 标记历史消息已完成发送
            sseMap[sessionPhasePair]?.let {
                it.right = true
            }

            // 将当前消息添加到队列
            val pendingKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
            stringRedisTemplate.opsForList().rightPush(pendingKey, agentMessage.toJson())

            // 处理队列中的消息
            processAgentPendingMessageMessages(sessionCode, phaseCode)

        } catch (e: InterruptedException) {
            logger.error("BizStreamMessage:sendSseWriteBackMessage InterruptedException", e)

            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        } finally {
            if (historyLock.isLocked && historyLock.isHeldByCurrentThread) {
                historyLock.unlock()
            }
        }

    }

    fun emitPhaseMessageFormDB(
        sessionCode: Long,
        phaseCode: Long,
        blocks: List<GalaxyBlockEntity>
    ) {
        var index = 0L
        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    val message = AgentContentBlockSystemPrompt().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.USER -> {
                    val message = AgentContentBlockUser().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }


                BlockType.TEXT -> {
                    val blockMessage = AgentContentBlockText().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentText().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.MCP_TOOL_CALL -> {
                    val blockMessage = AgentContentBlockToolCall().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.MCP_TOOL_RESULT -> {
                    val blockMessage = AgentContentBlockToolResult().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, blockMessage)) {
                        return
                    }

                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.DIFY_CALL -> {
                    val message = AgentContentBlockDifyCall().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }

                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.DIFY_RESULT -> {
                    val message = AgentContentBlockDifyResult().apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }

                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val message = AgentContentProtocol.parseJson(block.content!!).apply {
                        this._sessionCode = block.sessionCode
                        this._phaseCode = block.phaseCode
                        this.index = index++
                        this._blockCode = block.blockCode
                    }
                    if (sendStream(sessionCode, phaseCode, message)) {
                        return
                    }

                    break
                }
            }

        }
    }

    // 处理队列中的消息
    private fun processAgentPendingMessageMessages(sessionCode: Long, phaseCode: Long) {
        val pendingMessageKey = getAgentPendingMessageCacheKey(sessionCode, phaseCode)
        val pendingLock = ReentrantLock()

        try {
            if (!pendingLock.tryLock()) {
                // 等100ms后再重试一次
                TimeUnit.MILLISECONDS.sleep(100)
                val queueSize = stringRedisTemplate.opsForList().size(pendingMessageKey) ?: 0
                if (queueSize > 0) {
                    // 队列中还有消息，重新尝试处理
                    processAgentPendingMessageMessages(sessionCode, phaseCode)
                }
                return
            }
            // 循环处理队列中的所有消息
            while (true) {
                val pendingJson = stringRedisTemplate.opsForList().leftPop(pendingMessageKey)
                if (StringUtils.isBlank(pendingJson)) {
                    break
                }
                val pendingMessage = AgentContentProtocol.parseJson(pendingJson!!)

                if (sendStream(sessionCode, phaseCode, pendingMessage)) {
                    break
                }

            }
        } catch (e: InterruptedException) {
            logger.error("BizStreamMessage:processAgentPendingMessageMessages InterruptedException", e)

            Thread.currentThread().interrupt()
            throw GalaxyException(ErrorCode.THREAD_INTERRUPTED)
        } finally {
            if (pendingLock.isLocked && pendingLock.isHeldByCurrentThread) {
                pendingLock.unlock()
            }
        }
    }

    /**
     * 返回是否需要停止
     */
    private fun sendStream(sessionCode: Long, phaseCode: Long, agentMessage: AgentContentProtocol): Boolean {
        val (emitter, _) = sseMap[Pair(sessionCode, phaseCode)] ?: return true
        try {
            emitter.send(ResultVo.ok(agentMessage))

            // 如果是结束消息，完成发送并释放资源
            if (agentMessage._type in setOf(
                    AgentContentType.FINISH,
                    AgentContentType.ERROR,
                    AgentContentType.EXCEPTION
                )
            ) {
                releaseStreamChannel(sessionCode, phaseCode)
                emitter.complete()

                return true
            }

            return false

        } catch (e: Exception) {
            logger.error("BizStreamMessage:doSend Exception", e)

            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()

            return true
        }
    }

    private fun <T> sendAndStopStream(sessionCode: Long, phaseCode: Long, resultVo: ResultVo<T>? = null) {
        val (emitter, _) = sseMap[Pair(sessionCode, phaseCode)] ?: return
        try {
            resultVo?.let { emitter.send(it) }

            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()
        } catch (e: Exception) {
            logger.error("BizStreamMessage:doSendAndStop Exception", e)
            releaseStreamChannel(sessionCode, phaseCode)
            emitter.complete()
        }
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}