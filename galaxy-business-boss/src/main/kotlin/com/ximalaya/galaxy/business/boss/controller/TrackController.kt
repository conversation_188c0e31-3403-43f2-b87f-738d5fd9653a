package com.ximalaya.galaxy.business.boss.controller

import com.ximalaya.common.auth.common.subject.UserContext
import com.ximalaya.galaxy.business.boss.support.buildFailedResult
import com.ximalaya.galaxy.business.business.BizAlbumTrack
import com.ximalaya.galaxy.business.business.boss.BizGalaxyBoss
import com.ximalaya.galaxy.business.business.vo.QueryPageRequestVo
import com.ximalaya.galaxy.business.business.vo.ResultVo
import com.ximalaya.galaxy.business.business.vo.SaveTrackContentVo
import com.ximalaya.galaxy.business.business.vo.TrackVo
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.validation.BindingResult
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

/**
 *<AUTHOR>
 *@create 2025-06-02 17:07
 */
@Api(tags = ["声音API"])
@RestController
@RequestMapping("/api/track")
class TrackController(
    private val bizAlbumTrack: BizAlbumTrack,
    private val bizGalaxyBoss: BizGalaxyBoss,
) {

    @ApiOperation(value = "分页获取声音")
    @GetMapping("/session/{sessionCode}/page")
    fun getTracks(
        @RequestParam("sessionCode") sessionCode: Long,
        @RequestParam("pageNum", required = false) pageNum: Long?,
        @RequestParam("pageSize", required = false) pageSize: Long?
    ): ResultVo<GalaxyPage<TrackVo?>> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        val pageRequest = QueryPageRequestVo()
        pageNum?.let {
            pageRequest.pageNum = it
        }
        pageSize?.let {
            pageRequest.pageSize = it
        }

        return ResultVo.ok(bizAlbumTrack.getTracks(uid, sessionCode, pageRequest))
    }

    @ApiOperation(value = "获取声音列表")
    @GetMapping("/session/{sessionCode}/list")
    fun getTracks(
        @PathVariable("sessionCode") sessionCode: Long,
    ): ResultVo<List<TrackVo?>> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizAlbumTrack.getTracks(uid, sessionCode))
    }

    @ApiOperation(value = "获取声音")
    @GetMapping("/session/{sessionCode}")
    fun getTrack(
        @PathVariable sessionCode: Long,
        @RequestParam phaseName: String,
    ): ResultVo<TrackVo> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizAlbumTrack.getTrack(uid, sessionCode, phaseName))
    }

    @ApiOperation(value = "更新声音内容")
    @PostMapping("/session/{sessionCode}/phase/{phaseCode}")
    fun updateTrack(
        @PathVariable sessionCode: Long,
        @PathVariable phaseCode: Long,
        @Valid @RequestBody vo: SaveTrackContentVo,
        bindingResult: BindingResult
    ): ResultVo<Unit> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return when (bindingResult.hasErrors()) {
            true -> bindingResult.buildFailedResult()
            false -> {

                bizAlbumTrack.updateTrack(
                    uid,
                    sessionCode,
                    phaseCode,
                    vo,
                ) { id, session ->
                    bizGalaxyBoss.checkSessionUid(id, session)
                }
                ResultVo.ok()
            }
        }
    }

    @ApiOperation(value = "删除声音")
    @DeleteMapping("/session/{sessionCode}")
    fun removeTrack(
        @PathVariable sessionCode: Long,
        @RequestParam phaseName: String,
    ): ResultVo<Boolean> {
        val uid = UserContext.getUid() ?: return ResultVo.failed(ErrorCode.CONTENT_NOT_FOUND, "获取不到当前用户UID")

        return ResultVo.ok(bizAlbumTrack.removeTrack(uid, sessionCode, phaseName))
    }

}