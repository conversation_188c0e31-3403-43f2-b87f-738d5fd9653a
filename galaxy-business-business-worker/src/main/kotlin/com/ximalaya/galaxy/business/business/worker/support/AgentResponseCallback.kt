package com.ximalaya.galaxy.business.business.worker.support

import com.alibaba.fastjson.JSON
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import mu.KotlinLogging
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Response
import org.springframework.data.redis.core.StringRedisTemplate
import java.io.IOException

/**
 *<AUTHOR>
 *@create 2025-05-22 10:43
 */
class AgentResponseCallback(
    private val sessionCode: Long,
    private val phaseCode: Long,
    private val bizGalaxyWorker: BizGalaxyWorker,
    private val blockService: GalaxyBlockService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val contentDetector: ContentDetector,
    private val sendAndRetry: (AgentContentProtocol) -> Unit,
    private val sendAndSaveException: (exception: GalaxyException) -> AgentContentProtocol,
) : Callback {

    private fun getMessageIndex(): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    override fun onFailure(call: Call, e: IOException) {
        logger.error("[Agent] IO异常", e)

        val errorMessage = sendAndSaveException(GalaxyException(ErrorCode.CALL_AGENT_ERROR, "IO异常"))
        bizGalaxyWorker.saveAllBlocks(sessionCode, phaseCode, errorMessage)
    }

    override fun onResponse(call: Call, response: Response) {
        response.body()?.source()?.let { source ->
            try {
                val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
                if (!response.isSuccessful) {
                    throw GalaxyException(ErrorCode.CALL_AGENT_ERROR, "IO异常")
                }

                var previousBlockType: BlockType? = null
                var previousBlockCode: Long? = null

                var auditContent = ""
                val auditBlockCodes = mutableSetOf<Long>()

                while (!source.exhausted()) {

                    val line = source.readUtf8LineStrict()

                    // 处理SSE格式数据
                    if (!line.startsWith("data: ")) {
                        continue
                    }

                    val data = line.substring(6).trim()
                    logger.info("收到Agent消息: $data")

                    val agentMessage = AgentContentProtocol.parseJson(data)

                    // 根据类型创建数据块
                    when (agentMessage._type) {

                        AgentContentType.TEXT, AgentContentType.TEXT_DELTA -> {
                            auditContent += if (agentMessage._type == AgentContentType.TEXT_DELTA) {
                                (agentMessage as AgentContentTextDelta).textDelta
                            } else {
                                // 防御 应该不会出现
                                (agentMessage as AgentContentText).text
                            }

                            if (BlockType.TEXT == previousBlockType) {
                                agentMessage._blockCode = previousBlockCode
                            } else {
                                // 创建块
                                val blockCode = blockService.createBlock(sessionCode, phaseCode, BlockType.TEXT)

                                previousBlockType = BlockType.TEXT
                                previousBlockCode = blockCode

                                // 发送块消息
                                val blockMessage = AgentContentBlockText().apply {
                                    this._sessionCode = <EMAIL>
                                    this._phaseCode = <EMAIL>
                                    this.index = getMessageIndex() + 1
                                    this._blockCode = blockCode
                                }

                                // 存储块消息到Redis list
                                stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                                sendAndRetry(blockMessage)

                                agentMessage._blockCode = blockCode
                            }

                        }

                        AgentContentType.TOOL_CALL -> {
                            auditContent += agentMessage.toJson()

                            // 创建块
                            val blockCode = blockService.createBlock(
                                sessionCode,
                                phaseCode,
                                BlockType.MCP_TOOL_CALL,
                                agentMessage.toJson()
                            )
                            agentMessage._blockCode = blockCode

                            previousBlockType = BlockType.MCP_TOOL_CALL
                            previousBlockCode = blockCode

                            // 发送块消息
                            val blockMessage = AgentContentBlockToolCall().apply {
                                this._sessionCode = <EMAIL>
                                this._phaseCode = <EMAIL>
                                this.index = getMessageIndex() + 1
                                this._blockCode = blockCode
                            }

                            // 存储块消息到Redis list
                            stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                            sendAndRetry(blockMessage)
                        }

                        AgentContentType.TOOL_RESULT -> {
                            auditContent += agentMessage.toJson()

                            // 创建块
                            val blockCode = blockService.createBlock(
                                sessionCode,
                                phaseCode,
                                BlockType.MCP_TOOL_RESULT,
                                agentMessage.toJson()
                            )
                            agentMessage._blockCode = blockCode

                            previousBlockType = BlockType.MCP_TOOL_CALL
                            previousBlockCode = blockCode

                            // 发送块消息
                            val blockMessage = AgentContentBlockToolResult().apply {
                                this._sessionCode = <EMAIL>
                                this._phaseCode = <EMAIL>
                                this.index = getMessageIndex() + 1
                                this._blockCode = blockCode
                            }

                            // 存储块消息到Redis list
                            stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                            sendAndRetry(blockMessage)
                        }

                        AgentContentType.ERROR -> {
                            // 创建块
                            val blockCode = blockService.createBlock(
                                sessionCode,
                                phaseCode,
                                BlockType.ERROR,
                                agentMessage.toJson()
                            )

                            // 发送块消息
                            val blockMessage = AgentContentError().apply {
                                this._sessionCode = <EMAIL>
                                this._phaseCode = <EMAIL>
                                this._blockCode = blockCode
                                this.index = getMessageIndex() + 1
                            }

                            // 存储块消息到Redis list
                            stringRedisTemplate.opsForList().rightPush(messageKey, blockMessage.toJson())
                            sendAndRetry(blockMessage)
                        }

                        else -> {
                            /* empty line */
                        }

                    }

                    agentMessage._sessionCode = <EMAIL>
                    agentMessage._phaseCode = <EMAIL>
                    agentMessage.index = getMessageIndex() + 1

                    if (agentMessage._type == AgentContentType.PING) {
                        sendAndRetry(agentMessage)

                        continue
                    }
                    // 存储消息到Redis list
                    stringRedisTemplate.opsForList().rightPush(messageKey, agentMessage.toJson())
                    sendAndRetry(agentMessage)

                    agentMessage._blockCode?.let {
                        auditBlockCodes.add(it)
                    }

                    if (auditContent.length >= 100) {
                        // 审核
                        val auditResult = auditText(auditContent, auditBlockCodes)
                        if (auditResult.first) {
                            auditContent = ""
                            auditBlockCodes.clear()
                        } else {
                            throw auditResult.second!!
                        }
                    }

                    if (agentMessage._type in setOf(AgentContentType.FINISH, AgentContentType.ERROR)) {
                        // 最后的审核
                        val auditResult = auditText(auditContent, auditBlockCodes)
                        if (!auditResult.first) {
                            throw auditResult.second!!
                        }

                        // 主动断开HTTP连接
                        call.cancel()
                        bizGalaxyWorker.saveAllBlocks(sessionCode, phaseCode, agentMessage)
                        break
                    }

                }

            } catch (e: Exception) {
                logger.error("[Agent] 发生异常", e)

                // 主动断开HTTP连接
                call.cancel()

                val errorMessage = if (e is GalaxyException) {
                    sendAndSaveException(e)
                } else {
                    sendAndSaveException(GalaxyException(ErrorCode.CALL_AGENT_ERROR, e.message ?: e.toString()))
                }

                bizGalaxyWorker.saveAllBlocks(sessionCode, phaseCode, errorMessage)

            } finally {
                // 任务完成或失败，删除锁
                stringRedisTemplate.delete(RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode))
            }
        }
    }

    private fun auditText(
        auditContent: String,
        auditBlockCodes: Set<Long>
    ): Pair<Boolean, GalaxyException?> {
        val detectDetail = contentDetector.detectDetail(auditContent)
        val detectResponse = detectDetail.second
        if (detectResponse.result == 0) {
            auditBlockCodes.forEach {
                blockService.updateAuditBlockResult(
                    sessionCode,
                    phaseCode,
                    it,
                    AuditState.SUCCESS,
                    JSON.toJSONString(detectDetail.first),
                    JSON.toJSONString(detectDetail.second)
                )
            }
            return Pair(true, null)
        }

        logger.error(
            "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
            sessionCode,
            phaseCode,
            auditContent
        )

        auditBlockCodes.forEach {
            blockService.updateAuditBlockResult(
                sessionCode,
                phaseCode,
                it,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        }

        return Pair(false, GalaxyException(ErrorCode.CONTENT_ILLEGAL))
    }

    companion object {

        private val logger = KotlinLogging.logger { }

    }

}