package com.ximalaya.galaxy.business.business.worker.impl

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.TypeReference
import com.ximalaya.danube.core.model.DanubeMessage
import com.ximalaya.danube.core.producer.RocketProducer
import com.ximalaya.galaxy.business.business.BizAgent
import com.ximalaya.galaxy.business.business.support.TransactionExecutor
import com.ximalaya.galaxy.business.business.worker.BizGalaxyWorker
import com.ximalaya.galaxy.business.business.worker.WorkerFootballConfigBean
import com.ximalaya.galaxy.business.business.worker.support.AgentResponseCallback
import com.ximalaya.galaxy.business.business.worker.support.ToolJobHandler
import com.ximalaya.galaxy.business.common.ALBUM_OUTLINE
import com.ximalaya.galaxy.business.common.enums.ErrorCode
import com.ximalaya.galaxy.business.common.exception.GalaxyException
import com.ximalaya.galaxy.business.common.exception.GalaxyPhaseNotStartException
import com.ximalaya.galaxy.business.common.support.GalaxyAsserts
import com.ximalaya.galaxy.business.repo.dto.MetadataDto
import com.ximalaya.galaxy.business.repo.entity.GalaxyBlockEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxyPhaseEntity
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity
import com.ximalaya.galaxy.business.repo.enums.AuditState
import com.ximalaya.galaxy.business.repo.enums.BlockType
import com.ximalaya.galaxy.business.repo.enums.LogicDeleted
import com.ximalaya.galaxy.business.repo.enums.PhaseState
import com.ximalaya.galaxy.business.repo.service.GalaxyBlockService
import com.ximalaya.galaxy.business.repo.service.GalaxyPhaseService
import com.ximalaya.galaxy.business.repo.service.GalaxySessionService
import com.ximalaya.galaxy.business.repo.service.GalaxyToolService
import com.ximalaya.galaxy.business.service.ContentDetector
import com.ximalaya.galaxy.business.service.DifyService
import com.ximalaya.galaxy.business.service.enums.AgentContentType
import com.ximalaya.galaxy.business.service.enums.AgentMessageRole
import com.ximalaya.galaxy.business.service.support.RedisLockHelper
import com.ximalaya.galaxy.business.service.support.getAgentMessageCacheKey
import com.ximalaya.galaxy.business.service.vo.*
import com.ximalaya.galaxy.business.worker.api.model.CommonJobResponse
import com.ximalaya.galaxy.business.worker.api.model.StartSessionJobRequest
import com.ximalaya.galaxy.business.worker.api.model.StartToolJobRequest
import com.ximalaya.hot.track.service.vo.DifyRequestVo
import com.ximalaya.hot.track.service.vo.DifyResponseVo
import mu.KotlinLogging
import org.apache.commons.collections4.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RedissonClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Repository
import org.springframework.transaction.support.TransactionTemplate
import java.nio.charset.StandardCharsets
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.RejectedExecutionException
import java.util.concurrent.TimeUnit

/**
 *<AUTHOR>
 *@create 2025-05-20 16:43
 */
@Repository
class BizGalaxyWorkerImpl(
    private val transactionTemplate: TransactionTemplate,
    private val footballConfigBean: WorkerFootballConfigBean,
    @Qualifier("workerExecutor") private val workerExecutor: ThreadPoolTaskExecutor,
    @Qualifier("pg_galaxy_business_worker_topic_galaxy_realtime_messageRocketProducer") private val rocketProducer: RocketProducer,
    private val contentDetector: ContentDetector,
    private val difyService: DifyService,
    private val stringRedisTemplate: StringRedisTemplate,
    private val redissonClient: RedissonClient,
    private val bizAgent: BizAgent,
    private val sessionService: GalaxySessionService,
    private val phaseService: GalaxyPhaseService,
    private val blockService: GalaxyBlockService,
    private val toolService: GalaxyToolService,
) : BizGalaxyWorker {

    private fun aroundJobExecute(
        sessionCode: Long,
        phaseCode: Long,
        jobExecute: (GalaxySessionEntity, GalaxyPhaseEntity) -> Unit
    ): CommonJobResponse {
        val response = CommonJobResponse()

        val session = sessionService.selectBySessionCode(sessionCode)
        GalaxyAsserts.assertNotNull(session, ErrorCode.SESSION_ERROR, "会话不存在")

        val phase = phaseService.selectBySessionCodeAndPhaseCode(sessionCode, phaseCode)
        GalaxyAsserts.assertNotNull(phase, ErrorCode.PHASE_ERROR, "阶段不存在")

        val uid = session!!.uid!!

        // 限流区分内部、外部用户
        val isInner = footballConfigBean.isInnerUser(uid)
        val maxActivePhases = if (isInner) INNER_LIMIT else OUTER_LIMIT

        // 使用Redisson的RPermitExpirableSemaphore实现限流
        val semaphoreName = RedisLockHelper.getRateLimitLockKey(uid)
        val semaphore = redissonClient.getPermitExpirableSemaphore(semaphoreName)
        // 确保信号量存在并设置最大许可数
        if (!semaphore.isExists) {
            semaphore.trySetPermits(maxActivePhases)
        }
        // 为信号量对象设置过期时间
        redissonClient.keys.expire(semaphoreName, 1, TimeUnit.HOURS)

        // 尝试获取许可，超时时间为0表示不等待
        val permitId = semaphore.tryAcquire(0, 20, TimeUnit.MINUTES)
        if (permitId == null) {
            logger.warn("用户活动阶段数超出限制, uid: {}, maxActivePhases: {}", uid, maxActivePhases)
            response.code = ErrorCode.RATE_LIMIT.code
            response.message =
                if (isInner) "您已达到内部用户限制(最多${INNER_LIMIT}个任务同时运行)" else "已限流，请稍后重试~"
            return response
        }

        val ongoingRedisKey = RedisLockHelper.getPhaseOngoingLockKey(sessionCode, phaseCode)
        // 使用 setnx 操作替代 bucket，设置有效期为 20 分钟
        val isLockAcquired = stringRedisTemplate.opsForValue()
            .setIfAbsent(ongoingRedisKey, phaseCode.toString(), 20, TimeUnit.MINUTES)
        if (isLockAcquired != true) {
            logger.warn("任务重复执行, Session: {}, Phase: {}", sessionCode, phaseCode)

            response.code = ErrorCode.LOCK_BUSY.code
            response.message = ErrorCode.LOCK_BUSY.message
            return response
        }

        try {
            workerExecutor.execute {
                try {
                    TransactionExecutor.create(transactionTemplate)
                        .executeRequiresNewAndThrow { _ ->
                            updatePhaseState(sessionCode, phaseCode, PhaseState.RUNNING)
                            jobExecute(session, phase!!)
                        }
                } catch (e: GalaxyPhaseNotStartException) {
                    updatePhaseState(sessionCode, phaseCode, PhaseState.FINISHED)
                } catch (e: Exception) {
                    val errorMessage = if (e is GalaxyException) {
                        sendAndSaveException(sessionCode, phaseCode, e)
                    } else {
                        sendAndSaveException(
                            sessionCode,
                            phaseCode,
                            GalaxyException(ErrorCode.CALL_DIFY_ERROR, e.message ?: e.toString())
                        )
                    }

                    saveAllBlocks(sessionCode, phaseCode, errorMessage)
                } finally {
                    // 任务完成后释放许可
                    try {
                        semaphore.release(permitId)
                    } catch (e: Exception) {
                        logger.error("释放任务信号量许可失败", e)
                    }
                }
            }

            response.code = 200
            response.message = "成功"
        } catch (re: RejectedExecutionException) {
            // 如果执行被拒绝，释放许可和锁
            try {
                semaphore.release(permitId)
            } catch (e: Exception) {
                logger.error("释放任务信号量许可失败", e)
            }

            response.code = ErrorCode.RATE_LIMIT.code
            response.message = ErrorCode.RATE_LIMIT.message
        }

        return response
    }


    override fun runSessionJob(request: StartSessionJobRequest): CommonJobResponse {
        return aroundJobExecute(request.sessionCode, request.phaseCode) { _, phase ->
            // 先清空Redis list存储流式结果
            stringRedisTemplate.delete(getAgentMessageCacheKey(request.sessionCode, request.phaseCode))

            // 专辑大纲流程
            if (phase.phaseName == ALBUM_OUTLINE) {
                startPhaseJob(request)
                return@aroundJobExecute
            }

            val allPhases = phaseService.selectBySessionCode(request.sessionCode)
            val albumOutlinePhase = allPhases.find { it.phaseName == ALBUM_OUTLINE }
            if (albumOutlinePhase == null) {
                sendException(
                    request.sessionCode,
                    request.phaseCode,
                    GalaxyException(ErrorCode.PHASE_ERROR, "没有找到专辑大纲流程！！！")
                )

                throw GalaxyPhaseNotStartException("没有找到专辑大纲流程！！！")
            }

            if (albumOutlinePhase.phaseState != PhaseState.FINISHED.code) {
                sendException(
                    request.sessionCode,
                    request.phaseCode,
                    GalaxyException(ErrorCode.PHASE_ERROR, "专辑大纲流程没有完成！！！")
                )

                throw GalaxyPhaseNotStartException("专辑大纲流程没有完成！！！")
            }

            startPhaseJob(request)

            return@aroundJobExecute
        }
    }

    override fun runToolJob(request: StartToolJobRequest): CommonJobResponse {
        return aroundJobExecute(request.sessionCode, request.phaseCode) { session, _ ->
            // 先清空Redis list存储流式结果
            stringRedisTemplate.delete(getAgentMessageCacheKey(request.sessionCode, request.phaseCode))

            // 检查可删除的块是否可以删除
            if (CollectionUtils.isNotEmpty(request.needRemoveBlockCodes)) {
                // 检查数量必须是双数
                if (request.needRemoveBlockCodes.size % 2 != 0) {
                    logger.error(
                        "需要删除的块必须成对出现, Session: {}, Phase: {}, BlockCodes: {}",
                        request.sessionCode,
                        request.phaseCode,
                        request.needRemoveBlockCodes
                    )
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        GalaxyException(ErrorCode.CALL_DIFY_ERROR, "需要删除的块必须成对出现")
                    )

                    throw GalaxyPhaseNotStartException("需要删除的块必须成对出现")
                }

                val blocks = blockService.ktQuery()
                    .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
                    .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
                    .`in`(GalaxyBlockEntity::blockCode, request.needRemoveBlockCodes)
                    .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
                    .list()

                val canRemoves = blocks.filter {
                    it.blockType == BlockType.DIFY_CALL.code || it.blockType == BlockType.DIFY_RESULT.code
                }

                val callCounts = canRemoves.count {
                    it.blockType == BlockType.DIFY_CALL.code
                }
                val resultCounts = canRemoves.count {
                    it.blockType == BlockType.DIFY_RESULT.code
                }


                if (blocks.size != request.needRemoveBlockCodes.size || callCounts != resultCounts) {
                    logger.error(
                        "需要删除的块不存在或不是成对出现的, Session: {}, Phase: {}, BlockCodes: {}",
                        request.sessionCode,
                        request.phaseCode,
                        request.needRemoveBlockCodes
                    )
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        GalaxyException(ErrorCode.CALL_DIFY_ERROR, "需要删除的块不存在或不是成对出现的")
                    )

                    throw GalaxyPhaseNotStartException("需要删除的块不存在或不是成对出现的")
                }

            }

            startToolJob(request, session.uid!!)
        }
    }

    override fun saveAllBlocks(sessionCode: Long, phaseCode: Long, lastMessage: AgentContentProtocol) {
        // 更新阶段状态
        if (lastMessage._type == AgentContentType.FINISH) {
            updatePhaseState(sessionCode, phaseCode, PhaseState.FINISHED)
        } else {
            updatePhaseState(sessionCode, phaseCode, PhaseState.FAILED)
        }

        // 从Redis List获取该阶段的所有消息
        val messageRedisKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

        if (listSize > 0) {
            // 获取Redis list中的所有消息
            val messageJsons = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)

            if (messageJsons.isNullOrEmpty()) {
                return
            }

            val messageVos = messageJsons.map {
                AgentContentProtocol.parseJson(it)
            }.filter {
                // exception 不需要在此落库
                AgentContentType.EXCEPTION != it._type
            }

            val blockCodeMessagesMapping = messageVos.groupBy { it._blockCode }

            for (codeMessages in blockCodeMessagesMapping.entries) {
                if (codeMessages.key == null) {
                    // 没有blockCode的 不需要落库
                    continue
                }

                val blockCode = codeMessages.key!!
                val messages = codeMessages.value.filter {
                    setOf(
                        AgentContentType.TEXT,
                        AgentContentType.TEXT_DELTA,
                    ).contains(it._type)
                }.sortedBy { it.index }

                var content = ""
                for (message in messages) {
                    when (message._type) {
                        AgentContentType.TEXT -> {
                            content += (message as AgentContentText).text
                        }

                        AgentContentType.TEXT_DELTA -> {
                            content += (message as AgentContentTextDelta).textDelta
                        }

                        else -> {
                            /* empty line */
                        }
                    }
                }

                if (StringUtils.isNotBlank(content)) {
                    blockService.updateBlock(
                        sessionCode,
                        phaseCode,
                        blockCode,
                        content
                    )
                }

                logger.debug(
                    "持久化消息到数据库, sessionCode: {}, phaseCode: {}, blockCode: {}",
                    sessionCode, phaseCode, blockCode
                )
            }

            // 清空Redis缓存
            stringRedisTemplate.delete(messageRedisKey)
        }
    }


    override fun updatePhaseState(sessionCode: Long, phaseCode: Long, phaseState: PhaseState): Boolean {
        return phaseService.ktUpdate()
            .eq(GalaxyPhaseEntity::sessionCode, sessionCode)
            .eq(GalaxyPhaseEntity::phaseCode, phaseCode)
            .set(GalaxyPhaseEntity::phaseState, phaseState.code)
            .set(GalaxyPhaseEntity::updateTime, LocalDateTime.now())
            .update()
    }

    private fun buildDanubeMessage(
        sessionCode: Long,
        phaseCode: Long,
        agentMessage: AgentContentProtocol
    ): DanubeMessage {
        return DanubeMessage.createRocketMessage(
            null,
            "$sessionCode-$phaseCode",
            agentMessage.toJson().toByteArray(StandardCharsets.UTF_8)
        )
    }

    private fun startToolJob(request: StartToolJobRequest, uid: Long) {
        val toolEntity = toolService.selectById(request.toolId)
        if (toolEntity == null) {
            logger.error("工具不存在, Tool: {}", request.toolId)

            sendException(
                request.sessionCode,
                request.phaseCode,
                GalaxyException(ErrorCode.CALL_DIFY_ERROR, "工具: ${request.toolId} 不存在")
            )

            throw GalaxyPhaseNotStartException("工具: ${request.toolId} 不存在")
        }

        val metadata = toolEntity.metadata?.let {
            MetadataDto.parseJson(it)
        }

        if (StringUtils.isBlank(metadata?.appSecret)) {
            sendException(
                request.sessionCode,
                request.phaseCode,
                GalaxyException(ErrorCode.CALL_DIFY_ERROR, "工具: ${request.toolId} 密钥不存在")
            )

            throw GalaxyPhaseNotStartException("工具: ${request.toolId} 密钥不存在")
        }

        val difyRequest = DifyRequestVo().apply {
            this.appSecret = metadata?.appSecret
            this.user = uid.toString()
        }

        if (StringUtils.isNotBlank(request.args)) {
            difyRequest.inputs =
                JSON.parseObject(request.args, object : TypeReference<Map<String, String?>>() {})
        }

        // 使用Redis list存储流式结果
        val messageKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)
        // 输入空消息
        val initMessage = AgentContentPing().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.ts = System.currentTimeMillis()
        }
        stringRedisTemplate.opsForList().rightPush(messageKey, initMessage.toJson())

        // 为流式结果设置过期时间
        redissonClient.keys.expire(messageKey, 1, TimeUnit.HOURS)

        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockSystemPrompt().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.USER -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.TEXT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentText().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }.toJson())
                }

                BlockType.DIFY_CALL -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyCall().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.DIFY_RESULT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyResult().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    throw GalaxyPhaseNotStartException("上下文中发现错误，无法启动会话")
                }

                BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT -> {
                    stringRedisTemplate.opsForList()
                        .rightPush(messageKey, AgentContentProtocol.parseJson(block.content!!).toJson())
                }

            }
        }

        ToolJobHandler(
            request.sessionCode,
            request.phaseCode,
            request.needRemoveBlockCodes,
            request.args,
            difyRequest,
            difyService,
            this,
            blockService,
            stringRedisTemplate,
            contentDetector,
            {
                sendAndRetry(request.sessionCode, request.phaseCode, it)

            },
            {
                sendAndSaveException(request.sessionCode, request.phaseCode, it)
            }
        ).handle()
    }

    private fun startPhaseJob(request: StartSessionJobRequest) {
        // 使用Redis list存储流式结果
        val messageKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)

        // 输入空消息
        val initMessage = AgentContentPing().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.ts = System.currentTimeMillis()
        }
        stringRedisTemplate.opsForList().rightPush(messageKey, initMessage.toJson())

        // 为流式结果设置过期时间
        redissonClient.keys.expire(messageKey, 1, TimeUnit.HOURS)

        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.SYSTEM_PROMPT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockSystemPrompt().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.USER -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.content = block.content
                    }.toJson())
                }

                BlockType.TEXT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentText().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.text = block.content
                    }.toJson())
                }

                BlockType.DIFY_CALL -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyCall().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.args = DifyRequestVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.DIFY_RESULT -> {
                    stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockDifyResult().apply {
                        this._sessionCode = request.sessionCode
                        this._phaseCode = request.phaseCode
                        this.index = getMessageIndex(request.sessionCode, request.phaseCode)
                        this._blockCode = block.blockCode
                        this.result = DifyResponseVo.parseJson(block.content!!)
                    }.toJson())
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    throw GalaxyPhaseNotStartException("上下文中发现错误，无法启动会话")
                }

                 BlockType.MCP_TOOL_CALL, BlockType.MCP_TOOL_RESULT -> {
                    stringRedisTemplate.opsForList()
                        .rightPush(messageKey, AgentContentProtocol.parseJson(block.content!!).toJson())
                }

            }
        }

        // 写入prompt
        val blockCode = blockService.createBlock(request.sessionCode, request.phaseCode, BlockType.USER, request.prompt)

        // 审核prompt
        val detectDetail = contentDetector.detectDetail(request.prompt)
        val detectResponse = detectDetail.second
        val isLegal = detectResponse.result == 0
        if (isLegal) {
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.SUCCESS,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )
        } else {
            logger.error(
                "内容审核不通过, Session: {}, Phase: {}, Prompt: {}",
                request.sessionCode,
                request.phaseCode,
                request.prompt
            )
            blockService.updateAuditBlockResult(
                request.sessionCode,
                request.phaseCode,
                blockCode,
                AuditState.FAILED,
                JSON.toJSONString(detectDetail.first),
                JSON.toJSONString(detectDetail.second)
            )

            sendAndSaveException(request.sessionCode, request.phaseCode, GalaxyException(ErrorCode.CONTENT_ILLEGAL))
            // 阶段设置为错误
            updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
            return
        }

        // 添加system prompt到list
        stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode)
            // 写入system prompt
            this._blockCode = blockService.createBlockAutoAuditSuccess(
                request.sessionCode,
                request.phaseCode,
                BlockType.SYSTEM_PROMPT,
                request.systemPrompt
            )
            this.content = request.systemPrompt
        }.toJson())

        // 添加用户消息到list
        stringRedisTemplate.opsForList().rightPush(messageKey, AgentContentBlockUser().apply {
            this._sessionCode = request.sessionCode
            this._phaseCode = request.phaseCode
            this.index = getMessageIndex(request.sessionCode, request.phaseCode)
            this._blockCode = blockCode
            this.content = request.prompt
        }.toJson())

        val agentRequest = buildAgentChatRequest(request) ?: return
        bizAgent.chat(
            agentRequest,
            AgentResponseCallback(
                request.sessionCode,
                request.phaseCode,
                this,
                blockService,
                stringRedisTemplate,
                contentDetector,
                {
                    sendAndRetry(request.sessionCode, request.phaseCode, it)

                },
                {
                    sendAndSaveException(request.sessionCode, request.phaseCode, it)
                }
            )
        )
    }

    override fun sendException(
        sessionCode: Long,
        phaseCode: Long,
        galaxyException: GalaxyException
    ): AgentContentProtocol {
        val exceptionMessage = AgentContentGalaxyException().apply {
            this._sessionCode = sessionCode
            this._phaseCode = phaseCode
            this.index = getMessageIndex(sessionCode, phaseCode) + 1
            this.errorCode = galaxyException.errorCode
            this.errorMessage = galaxyException.message
        }

        // 存储错误消息到Redis list
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())

        sendAndRetry(sessionCode, phaseCode, exceptionMessage)

        return exceptionMessage
    }

    override fun sendAndSaveException(
        sessionCode: Long,
        phaseCode: Long,
        galaxyException: GalaxyException
    ): AgentContentProtocol {
        val exceptionMessage = AgentContentGalaxyException().apply {
            this._sessionCode = sessionCode
            this._phaseCode = phaseCode
            this.index = getMessageIndex(sessionCode, phaseCode) + 1
            this.errorCode = galaxyException.errorCode
            this.errorMessage = galaxyException.message
        }

        val blockCode =
            blockService.createBlock(sessionCode, phaseCode, BlockType.EXCEPTION, exceptionMessage.toJson())
        exceptionMessage._blockCode = blockCode

        // 存储错误消息到Redis list
        val messageKey = getAgentMessageCacheKey(sessionCode, phaseCode)
        stringRedisTemplate.opsForList().rightPush(messageKey, exceptionMessage.toJson())

        sendAndRetry(sessionCode, phaseCode, exceptionMessage)

        return exceptionMessage
    }

    private fun buildAgentChatRequest(request: StartSessionJobRequest): AgentChatRequestVo? {
        val messageRedisKey = getAgentMessageCacheKey(request.sessionCode, request.phaseCode)
        val listSize = stringRedisTemplate.opsForList().size(messageRedisKey) ?: 0

        if (listSize > 0) {
            val agentMessageVos = LinkedList<AgentMessageVo>()

            // 从Redis list中获取所有消息
            val cacheMessages = stringRedisTemplate.opsForList().range(messageRedisKey, 0, listSize - 1)

            if (CollectionUtils.isNotEmpty(cacheMessages)) {
                for (messageJson in cacheMessages!!) {
                    val message = AgentContentProtocol.parseJson(messageJson)

                    when (message._type) {
                        AgentContentType.BLOCK_USER -> {
                            agentMessageVos.add(AgentMessageVo(AgentMessageRole.USER).apply {
                                this.content = mutableListOf(
                                    AgentContentText().apply {
                                        this.text = (message as AgentContentBlockUser).content
                                    }
                                )
                            }
                            )
                        }

                        AgentContentType.PING, AgentContentType.FINISH, AgentContentType.BLOCK_SYSTEM_PROMPT, AgentContentType.BLOCK_TEXT, AgentContentType.BLOCK_TOOL_CALL, AgentContentType.BLOCK_TOOL_RESULT,
                        AgentContentType.BLOCK_DIFY_CALL, AgentContentType.BLOCK_DIFY_RESULT -> {
                            continue
                        }

                        AgentContentType.TEXT, AgentContentType.TEXT_DELTA -> {
                            if (agentMessageVos.isEmpty()) {
                                // 添加第一条消息
                                agentMessageVos.add(
                                    AgentMessageVo(AgentMessageRole.GPT).apply {
                                        this.content = mutableListOf(message)
                                    }
                                )
                                continue
                            }
                            val lastMessage = agentMessageVos.last()
                            if (lastMessage._role == AgentMessageRole.GPT) {
                                // 合并
                                val lastContent = lastMessage.content?.last() as AgentContentText
                                if (lastContent._type == AgentContentType.TEXT) {
                                    lastContent.text += (message as AgentContentText).text
                                } else {
                                    lastMessage.content!!.add(message)
                                }
                            } else {
                                // 添加
                                agentMessageVos.add(
                                    AgentMessageVo(AgentMessageRole.GPT).apply {
                                        this.content = mutableListOf(message)
                                    }
                                )
                            }

                        }

                        AgentContentType.TOOL_CALL -> {
                            if (agentMessageVos.isEmpty()) {
                                // 添加第一条消息
                                agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(message)
                                })
                                continue
                            }

                            val lastMessage = agentMessageVos.last()
                            if (lastMessage._role == AgentMessageRole.GPT) {
                                lastMessage.content!!.add(message)
                            } else {
                                // 添加
                                agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(message)
                                })
                            }

                        }

                        AgentContentType.TOOL_RESULT -> {
                            agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                                this.content = mutableListOf(message)
                            })
                        }


                        AgentContentType.ERROR, AgentContentType.EXCEPTION -> {
                            val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                            sendException(
                                request.sessionCode,
                                request.phaseCode,
                                exception
                            )
                            // 阶段设置为错误
                            updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
                            return null
                        }
                    }
                }

                return AgentChatRequestVo().apply {
                    this.system = request.systemPrompt
                    this.messages = agentMessageVos
                }
            }
        }

        // 如果Redis中没有缓存或缓存处理失败，则从数据库获取
        val blocks: List<GalaxyBlockEntity> = blockService.ktQuery()
            .eq(GalaxyBlockEntity::sessionCode, request.sessionCode)
            .eq(GalaxyBlockEntity::phaseCode, request.phaseCode)
            .eq(GalaxyBlockEntity::deletedAt, LogicDeleted.SAVE.getCode())
            .list()

        val agentMessageVos = LinkedList<AgentMessageVo>()

        for (block in blocks) {
            when (BlockType.parseCode(block.blockType)) {
                BlockType.USER -> {
                    agentMessageVos.add(
                        AgentMessageVo(AgentMessageRole.USER).apply {
                            this.content = mutableListOf(
                                AgentContentText().apply {
                                    this._blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        }
                    )
                }


                BlockType.TEXT -> {
                    if (agentMessageVos.isNotEmpty()) {
                        val lastMessage = agentMessageVos.last()
                        if (lastMessage._role == AgentMessageRole.GPT) {
                            lastMessage.content!!.add(
                                AgentContentText().apply {
                                    this._blockCode = block.blockCode
                                    this.text = block.content
                                }
                            )
                        } else {
                            agentMessageVos.add(
                                AgentMessageVo(AgentMessageRole.GPT).apply {
                                    this.content = mutableListOf(
                                        AgentContentText().apply {
                                            this._blockCode = block.blockCode
                                            this.text = block.content
                                        }
                                    )
                                }
                            )
                        }
                    } else {
                        agentMessageVos.add(
                            AgentMessageVo(AgentMessageRole.GPT).apply {
                                this.content = mutableListOf(
                                    AgentContentText().apply {
                                        this._blockCode = block.blockCode
                                        this.text = block.content
                                    }
                                )
                            }
                        )
                    }
                }

                BlockType.MCP_TOOL_CALL -> {
                    if (agentMessageVos.isEmpty()) {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                        continue
                    }

                    val lastMessage = agentMessageVos.last()
                    if (lastMessage._role == AgentMessageRole.GPT) {
                        lastMessage.content!!.add(AgentContentProtocol.parseJson(block.content!!))
                    } else {
                        // 添加
                        agentMessageVos.add(AgentMessageVo(AgentMessageRole.GPT).apply {
                            this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                        })
                    }
                }

                BlockType.MCP_TOOL_RESULT -> {
                    agentMessageVos.add(AgentMessageVo(AgentMessageRole.TOOL_RESULT).apply {
                        this.content = mutableListOf(AgentContentProtocol.parseJson(block.content!!))
                    })
                }

                BlockType.ERROR, BlockType.EXCEPTION -> {
                    val exception = GalaxyException(ErrorCode.CALL_AGENT_ERROR, "上下文中发现错误，无法启动会话")
                    sendException(
                        request.sessionCode,
                        request.phaseCode,
                        exception
                    )
                    // 阶段设置为错误
                    updatePhaseState(request.sessionCode, request.phaseCode, PhaseState.FAILED)
                    return null
                }

                else -> continue
            }
        }

        return AgentChatRequestVo().apply {
            this.system = request.systemPrompt
            this.messages = agentMessageVos
        }
    }

    private fun getMessageIndex(sessionCode: Long, phaseCode: Long): Long {
        return stringRedisTemplate.opsForList().size(getAgentMessageCacheKey(sessionCode, phaseCode)) ?: 0
    }

    private fun sendAndRetry(sessionCode: Long, phaseCode: Long, agentMessage: AgentContentProtocol) {
        val maxRetries = 3
        var retryCount = 0

        while (retryCount < maxRetries) {
            try {
                val sendResult = rocketProducer.send(buildDanubeMessage(sessionCode, phaseCode, agentMessage))
                if (sendResult.isSuccess) {
                    return
                }

                logger.warn("[Galaxy] 消息发送失败，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode")
            } catch (e: Exception) {
                logger.error(
                    "[Galaxy] 消息发送异常，尝试第${retryCount + 1}次重试，sessionCode=$sessionCode, phaseCode=$phaseCode",
                    e
                )
            }

            retryCount++
            if (retryCount < maxRetries) {
                // 使用指数退避策略，每次重试间隔增加
                Thread.sleep((100L * (1 shl retryCount)).coerceAtMost(1000L))
            }
        }

        // 所有重试都失败了
        logger.error("[Galaxy] 消息发送最终失败，已重试${maxRetries}次，sessionCode=$sessionCode, phaseCode=$phaseCode, messageType=${agentMessage._type}")
        // todo 可以考虑将失败的消息保存到数据库或其他持久化存储中，以便后续处理
    }

    companion object {

        private val logger = KotlinLogging.logger { }

        private const val INNER_LIMIT = 10

        private const val OUTER_LIMIT = 1

    }
}