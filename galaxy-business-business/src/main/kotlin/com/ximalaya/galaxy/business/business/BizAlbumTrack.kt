package com.ximalaya.galaxy.business.business

import com.ximalaya.galaxy.business.business.vo.*
import com.ximalaya.galaxy.business.repo.dto.GalaxyPage
import com.ximalaya.galaxy.business.repo.entity.GalaxySessionEntity

/**
 *<AUTHOR>
 *@create 2025-06-01 15:35
 */
interface BizAlbumTrack {

    fun getAlbums(uid: Long, pageRequest: QueryPageRequestVo): GalaxyPage<AlbumVo?>

    fun getAlbum(uid: Long, sessionCode: Long): AlbumVo?

    fun saveAlbum(
        uid: Long,
        sessionCode: Long,
        blockCode: Long,
        saveVo: SaveAlbumVo,
        checkSessionUid: (Long, GalaxySessionEntity) -> Unit
    )

    fun removeAlbum(uid: Long, sessionCode: Long): Boolean

    fun getTracks(uid: Long, sessionCode: Long): List<TrackVo>?

    fun getTracks(uid: Long, sessionCode: Long, pageRequest: QueryPageRequestVo): GalaxyPage<TrackVo?>

    fun getTrack(uid: Long, sessionCode: Long, phaseName: String): TrackVo?

    fun getTrack(uid: Long, sessionCode: Long, phaseCode: Long): TrackVo?

    fun updateTrack(
        uid: Long,
        sessionCode: Long,
        phaseCode: Long,
        saveVo: SaveTrackContentVo,
        checkSessionUid: (Long, GalaxySessionEntity) -> Unit
    )

    fun saveTrackPhaseCode(id: Long, phaseCode: Long): Boolean

    fun removeTrack(uid: Long, sessionCode: Long, phaseName: String): Boolean

}